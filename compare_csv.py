import pandas as pd

def compare_csv_files(file1, file2):
    """
    Compares two CSV files and checks if the values in common columns are identical.

    Args:
        file1 (str): Path to the first CSV file.
        file2 (str): Path to the second CSV file.
    """
    try:
        df1 = pd.read_csv(file1)
        df2 = pd.read_csv(file2)
    except FileNotFoundError as e:
        print(f"Error: {e}")
        return

    common_columns = list(set(df1.columns) & set(df2.columns))

    if not common_columns:
        print("No common columns found between the two files.")
        return

    print(f"Common columns found: {', '.join(common_columns)}\n")

    all_equal = True
    for column in common_columns:
        # Align dataframes by index for comparison, in case they have different row orders
        temp_df1 = df1[[column]].sort_index()
        temp_df2 = df2[[column]].sort_index()

        if len(temp_df1) != len(temp_df2):
            print(f"Column '{column}': Lengths are different ({len(temp_df1)} vs {len(temp_df2)}). Cannot compare.")
            all_equal = False
            continue

        # Reset index to ensure direct comparison is valid if indices are not aligned
        comparison = temp_df1.reset_index(drop=True).equals(temp_df2.reset_index(drop=True))

        if comparison:
            print(f"Column '{column}': Values are identical.")
        else:
            print(f"Column '{column}': Values are NOT identical.")
            all_equal = False

    print("\n----------------------------------------")
    if all_equal:
        print("Conclusion: All common columns have identical values.")
    else:
        print("Conclusion: At least one common column has different values.")

if __name__ == "__main__":
    import sys

    if len(sys.argv) != 3:
        print("Usage: python compare_csv.py <file1.csv> <file2.csv>")
        sys.exit(1)

    file_path1 = sys.argv[1]
    file_path2 = sys.argv[2]

    compare_csv_files(file_path1, file_path2)