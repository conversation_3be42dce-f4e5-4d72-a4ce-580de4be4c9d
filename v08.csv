﻿"vin","ct","st","at","uuid","col","rct","r","ABSA","ABSF","ABSIO","ACAmbtTem","ACAmbtTemV","ACAutoDspCmd","ACCAccReqSts_CH","ACCAccReqSts_PT","ACCAccReqVal","AccelActuPos","AccelActuPosV","AccelerationXsecinfo","AccelerationYsecinfo","AccelerationZsecinfo","AccelOvrd","AcceZ","ACCIntervQuit_Rddy","ACComprActuPwr","ACComprMngmntInfo","ACCoolngFanPWMReq","ACCOtptShaftTotToqReqSts","ACCOtptShaftTotToqReqVal","ACCSysFltSts","ACCSysFltSts_PT","ACCSysSts","ACCSysSts_PT","ACEcoDspCmd","ACEvapoTem","ACFrtBlwrSpdDspCmd","ACFrtBlwrWorkPcnt","ACHepaDspCmd","ACHiSideFludPrs","ACHtrCoreTem","ACHtrPwr","ACInCarTem","ACLAirMdDspCmd","ACLoSideFludPrs","ACLTemDspCmd","ACOnOffDspCmd","ACPwr","ACRcctnDspMd","ACRcctnPcnt","ACRrBlwrLvlDspCmd","ACRrTemDspCmd","ACRTemDspCmd","ActualDampingCurr_FL","ActualDampingCurr_FR","ActualDampingCurr_RL","ActualDampingCurr_RR","ActuPnonAng","ActuRoadWhlAng","ActuRoadWhlAngRddy","ACWindTem","AEBActv","AEBDclReqSts","AEBDclReqVal","AEBDspCmd","AEBMsgReq","AEBPrflReq","AEBSysFltSts","AEBSysSts","AEBTrgtRcgntn","AGS1Pos","AGS2Pos","AGSFaultInfo","AIParkngDsp","AIParkngSts","AIPilotCndNotSt","AIPilotDrvrSeldTrgtDistLvl","AIPilotDrvrSelTrgtSpd","AIPiloTIntervQuit","AIPilotMsgIndcr","AIPilotSwSts","AIPilotSysFltSts","AIPilotSysSts","AIPilotWrnng","AIPilotWrnng_RollingCounter","AIPkgAPASCSAcclReqVal","AIPkgEPSRdWhlAngReqSts","AIPkgEPSRdWhlAngReqStsV","AIPkgEPSReqRdWhlAng","AIPltEPSRdWhlAngReqSts","AIPltEPSRdWhlAngReqStsRddy","AIPltEPSRdWhlAngReqStsV","AIPltEPSRdWhlAngReqStsVRddy","AIPltEPSReqRdWhlAng","AIPltEPSReqRdWhlAngRddy","AIPltEPSReqRdWhlAngV","AIPltEPSReqRdWhlAngVRddy","AirbagDpl","AirbagDplInvsn","AirbagSysFlt","AirbagSysFltIndCmd","AirCtrlrHVILClsd","AirCtrlrHVILClsdV","AirvntRZoneShtdwnReqDspCmd","AllMuteFdbk","AllMuteReq","AltitudeINSsecinfo","AltitudeRTK","AmbtTemSnsrFlrSts","AMP_NKI_INFOCAN","AMPAvlbly","AMPCanVoltSts","AMPClsAbNormalSts","AMPClsDNormalSts","AMPDiaVoltSts","AMPDspInitSts","AMPDspResetAgainSts","AMPDspResetSts","AMPDspSpiBusState","AMPDspSts","AMPEqmode","AMPFucVoltSts","AMPHWVersionSts","AMPMcuSpiBusRecSts","AMPMcuSpiBusSentSts","AMPMcuSysSts","AMPPwrRdySts","AMPStartupSts","AMPTurnOnRdyStsFdbk","AMR_NKI_BDCAN","AMRSts","AMRSysSts","AngularRateYsecinfo","APASCSAcclReqSts","APASCSAcclReqVal","APAShifterPosReqd","APCA","apca_height","ATC_NKI_BDCAN","ATCAvlbly","AutocParkngSwIndrCmd_l","AutocParkngSwReq","AutocParkngSwReq_l","AutodDrvngCndNotSt","AutodDrvngRestDist","AutodDrvngSysMdReq","AutodDrvngSysMdReqResp","AutodDrvngSysMdReqRespV","AutodDrvngSysSCSDrvOffReq","AutodDrvngSysSCSSdslReq","AutodDrvngSysSts_PT","AutodDrvngToqReqResp","AutoHoldMsg","AutoHoldSysSts","AutoLghtOnReq","AutoMainBeamLghtOn","AutoMainBeamLghtReq","AutoWlcmLghtA","BatAgngSta","BatCrnt","BatCrnt_R","BatIncnstncy","BatSOC","BatSOC_R","BatSOCSts","BatSOFVol1","BattCoolMangmntInfo","BatVol","BatVol_R","BatVolSts","BCM_BD_NKI","BCM_BD_NOI","BCM_BD_NWI","BCM_BDEx_NKI","BCM_BDEx_NOI","BCM_BDEx_NWI","BCM_BodyExtdAvlbly","BCM_NWI_BDCAN","BCM_NWI_BDEXTDCAN","BCMAvlbly","BCMBTCtrlPotcl","BCMCofignSts2","BCMDrvrDetSnsrFltSts","BCMDrvrDetSts","BCMNoSmtKeyInVehRmndr","BCMPutSmtKeyToBkupPosR","BCMPwrMdHwdSta","BCMRunCrkF","BCMSyncSmtKeyRmndr","BCMTakeSmtKeyOutOfSR","BCMVehLckUnlckActn","BleCmftLckSet","BleCmftUnlckSet","BlePEPSFnFlr","BleScurReq","BleScurResp","BlwFaultInfo","BMS_NKI_PTCANFD","BMS_NKI_PTEXTDCAN","BMS_NOI_PTCANFD","BMS_NOI_PTEXTDCAN","BMS_NWI_PTCANFD","BMS_NWI_PTEXTDCAN","BMSAltngChrgCrntDspCmd","BMSAvlblEnrg","BMSAvlblEnrgV","BMSBalancingStatus","BMSBatPrsAlrm","BMSBatPrsAlrmBkup","BMSBatPrsAlrmV","BMSBatPrsAlrmVBkup","BMSBatPrsFlt","BMSBatPrsSnsrV","BMSBatPrsSnsrVal","BMSBatPrsSnsrValBkup","BMSBatPrsSnsrVBkup","BMSBatteryDataInfo14_DA08","BMSBatteryDataInfo15_DA08","BMSBscSta","BMSBscStaBkup","BMSBusbarTempMax","BMSCellMaxTem","BMSCellMaxTemIndx","BMSCellMaxTemV","BMSCellMaxVol","BMSCellMaxVolIndx","BMSCellMaxVolV","BMSCellMinTem","BMSCellMinVol","BMSCellMinVolIndx","BMSCellMinVolV","BMSCellOverChrgdAlrm","BMSCellTem[n]","BMSCellVolSumNum","BMSCellVoltFlt","BMSChlrOffReq","BMSChrgBuf","BMSChrgCrntLmt","BMSChrgCrntLmtV","BMSChrgCtrlDspCmd","BMSChrgCtrlResp","BMSChrgOtptCrntReq","BMSChrgOtptCrntReqV","BMSChrgPeakPwr","BMSChrgPwrLmt","BMSChrgPwrLmtV","BMSChrgrPlugCnctnIO","BMSChrgSpRsn","BMSChrgSts","BMSChrgSts_l","BMSChrgStsIO","BMSChrgSttnMchngStaResp","BMSChrgTrgtAvlblEnrg","BMSChrgTrgtAvlblEnrgV","BMSChrgTrgtSOCDspCmd","BMSClntPumpPWMReq","BMSClntPumpSts","BMSClntTem","BMSClntTemV","BMSCMUFlt","BMSCumuDschrgEnrg","BMSCumuDschrgEnrgV","BMSDircPstvRlyCtrlReq","BMSDisTrgtAvlblEnrg","BMSDisTrgtAvlblEnrgV","BMSDschrgBuf","BMSDschrgBufV","BMSDschrgCrntLmt","BMSDschrgCtnsPwrLmt","BMSDsChrgCtrlResp","BMSDschrgPwrLmt","BMSDschrgPwrLmtBkup","BMSDschrgPwrLmtV","BMSDsChrgSpRsn","BMSDsChrgTrgtSOCDspCmd","BMSEmsnRltdMalfA","BMSEnbPTC","BMSEstdElecRng","BMSFlameGasStatus","BMSFltCt","BMSFltLamp","BMSFltLvl","BMSFltLvlBkup","BMSHighPrcsAvlblEnrg","BMSHighPrcsAvlblEnrgV","BMSHiSOCAlrm","BMSHVILAlrm","BMSHVILClsd","BMSHVILClsdBkup","BMSHVILSts","BMSHVILStsBkup","BMSHVReq","BMSHVReqBkup","BMSIndrtPstvRlyCtrlReq","BMSInptClntTem","BMSJumpngSOCAlrm","BMSKeepSysAwkScene","BMSLowPtIsltnRstcAlrm","BMSLowSOCAlrm","BMSLVBatInput","BMSMainRelaySts","BMSMainRelayStsBkup","BMSMaxVolLmt","BMSMaxVolLmtV","BMSMinVolLmt","BMSMinVolLmtV","BMSNegativeBusVolt","BMSNegPtIsltnRstc","BMSNegPtIsltnRstcBkup","BMSNegPtIsltnRstcV","BMSNegPtIsltnRstcVBkup","BMSNegRelayStatus","BMSOfbdChrgRelaySts","BMSOfbdChrgrPlugOn","BMSOfbdChrgSpRsn","BMSOfbdRlyCtrlReq","BMSOffbdChargerWakeup","BMSOnbdChargerWakeup","BMSOnbdChrgngCmdReq","BMSOnbdChrgrAltCrntLmt","BMSOnbdChrgRelaySts","BMSOnbdChrgrOtptCrntReq","BMSOnbdChrgrOtptVolReq","BMSOnbdChrgrPlugOn","BMSOnbdChrgSpRsn","BMSOverCellVolAlrm","BMSOverPackVolAlrm","BMSOverTemAlrm","BMSPackCrnt","BMSPackCrntBkup","BMSPackCrntV","BMSPackCrntVBkup","BMSPackCurrent2","BMSPackSOC","BMSPackSOCActual","BMSPackSOCBkup","BMSPackSOCDsp","BMSPackSOCDsp_l","BMSPackSOCDspV","BMSPackSOCDspV_l","BMSPackSOCV","BMSPackSOCVBkup","BMSPackSOH","BMSPackTemFlt","BMSPackVol","BMSPackVolBkup","BMSPackVolMsmchAlrm","BMSPackVoltFlt","BMSPackVolV","BMSPackVolVBkup","BMSPoorCellCnstncyAlrm","BMSPositiveBusVolt","BMSPosPtIsltnRstc","BMSPosPtIsltnRstcBkup","BMSPosPtIsltnRstcV","BMSPosPtIsltnRstcVBkup","BMSPosRelayStatus","BMSPreRelayStatus","BMSPreThrmFltInd","BMSPreThrmFltIndBkup","BMSPTCHeatReqDspCmd","BMSPTCHeatResp","BMSPtIsltnRstc","BMSPtIsltnRstcV","BMSPumpPwrOnReq","BMSPwrLmtChrctsPnt","BMSRelayStep","BMSReqPEBChrgVol","BMSReqPTCPwr","BMSReserChrgCtrlResp","BMSReserCtrlDspCmd","BMSReserStHourDspCmd","BMSResetSts","BMSStaOfHealthCapct","BMSStwrVer","BMSSysFltLamp","BMSTemOverDifAlrm","BMSUnderCellVolAlrm","BMSUnderPackVolAlrm","BMSVehicleWakeup","BMSWriteEECmpd","BMSWrlsChrgngCmd","BMSWrlsChrgngOtptCrnt","BMSWrlsChrgngOtptVol","BMSWrlsChrgngSfty","BMSWrlsChrgSpRsn","BMSWrnngInfo","BMSWrnngInfoBkup","BMSWrnngInfoCRC","BMSWrnngInfoPV","BMSWrnngInfoRC","BntOpenSts","BPEPS_NKI_BDEXTDCAN","BPEPS_NOI_BDEXTDCAN","BPEPS_NWI_BDEXTDCAN","BPEPSAvlbly","BPEPSCtrlPotcl","BrkFludLvlLow","BrkFludLvlLowV","BrkLghtOnReq","BrkPdlAppd","BrkPdlAppdV","BrkPdlDrvrAppdPrs","BrkPdlMdlSts","BrkPdlPos","BrkPdlPosV","BrkPdlSimltdPrs","BrkSysBrkLghtsReqd","BrkSysHillStAstSts","CARLog_NKI_INFOCAN","CCSwStsDistDecSwA","CCSwStsDistIncSwA","CCSwStsSpdDecSwA","CCSwStsSpdIncSwA","CCU_NKI_PTCANFD","CCU_NOI_PTCANFD","CCU_NWI_PTCANFD","CCUActiveWkup","CCUChrgngStsIndrMd_l","CCUDircPstvRelaySts","CCUEleccLckCtrlDspCmd","CCUIndrtPstvRelaySts","CCUOfbdChrgCapVol","CCUOfbdChrgRelaySts","CCUOfbdChrgRelayStsV","CCUOfbdChrgrSktVol","CCUOfbdChrgrWkup","CCUOfbdRelayWeldFlt","CCUOfbdRelayWeldFltCt","CCUOfbdRlyDiagResult","CCUOfbdRlyNgtvVolt","CCUOffBdChrgrPlugOn","CCUOffBdChrgrPlugOnV","CCUOnbdChrgrPlugOn","CCUOnBdChrgrSktElecLckSta","CCUOnBdChrgrSktElecLckStaV","CCUOnbdChrgrSpRsn","CCURawDTC","CellBTem_Sec00","CellFTem_Sec00","cellSignalStrength","ChACCAccReqResp","ChAEBDclReqResp","ChAEBPrflReqResp","ChillerEXVActPosition","ChllrInClntTemp","ChmSndId","ChmSndVolmStepReq","ChrgngDoorPosSts","ChrgngRmnngTime","ClstrDspdVehSpd","ClstrDspdVehSpd_l","CMSPosRclReq","CoDrvrDspCtrlReqICM","CoDrvrDspCtrlSts_l","ColMovSts_l","ColPosCmdReq_l","CompOutRefTem","ComprOnOffDspCmd","CoolingFanFaultInfo","CoolngFanPWMFdbk","CrntAvgElecCsump","CrntAvgElecCsumpV","CrntLoctnRoadCurve","DayTimeRunningLghtF","DCCurrentMaxHV","DCDCVolStpt","DCM_FL_NKI_BDCAN","DCM_FL_NOI_BDCAN","DCM_FL_NWI_BDCAN","DCM_FR_NKI_BDCAN","DCM_FR_NOI_BDCAN","DCM_FR_NWI_BDCAN","DCM_RL_NKI_BDCAN","DCM_RL_NOI_BDCAN","DCM_RL_NWI_BDCAN","DCM_RR_NKI_BDCAN","DCM_RR_NOI_BDCAN","DCM_RR_NWI_BDCAN","DCMFLPWLNotNormd","DCMFLWndDclnSpc","DCMFRWndDclnSpc","DCOverCurrentHV","DCOverCurrentLV","DCOverVoltHV","DCOverVoltLV","DCUnderVoltHV","DCUnderVoltLV","DCVoltFail","DfstOnDspCmd","DigKey1Loctn","DigKey2Loctn","DigKeyExeAvlbl","DigKeyExeAvlblV","DipdBeamLghtOn","DircnIndLampSwSts","DircnIndLampSwStsMntry","DLP_NarwSpcAstSts","DLP_NKI_BDCAN","DLP_RCWWrnng","DLPBndngAstnEnbSts","DLPCamrFltSts","DLPLghtRdySts","DoorLckCtrlReq","DoorLckCtrlReq_FR_l","DoorLckCtrlReq_l","DrvngScurReq","DrvngScurResp","DrvrDoorOpenSts","DrvrDoorOpenStsV","DrvrDspCtrlReqICM","DrvrDspCtrlSts_l","DrvrPosRclReq","DrvrPosRclSts","DrvrPosSaveReq","DrvrPosSpRclReq","DrvrPrsc","DrvrPWLInitnRmndr","DrvrReqOtptRodTrvl","DrvrSbltAtc","DrvrSbltAtcCRC","DrvrSbltAtcRC","DrvrSbltAtcV","DrvrSeatCtrlSts","DrvrSeatEasyEntASta","DrvrSeatEasyEntInhReq","DrvrSeatEasyEntInhSts","DrvrSeatHoztPosRclCmd","DrvrSeatHoztSwReq","DrvrSeatHoztSwReq_SW_l","DrvrSeatHoztSwReq_UI","DrvrSeatHoztSwReqV","DrvrSeatMmryCtrlPosRclSts","DrvrSeatPosGroupMmrySts","DrvrSeatPosGroupRclReq","DrvrSeatPosGroupRclSts","DrvrSeatPosGroupSaveReq","DrvrSeatPosGroupSaveSts","DrvrSeatPosRclCmd","DrvrSeatRclnPosRclCmd","DrvrSeatRclnSwReq","DrvrSeatRclnSwReq_SW_l","DrvrSeatRclnSwReq_UI","DrvrSeatTiltPosRclCmd","DrvrSeatTiltSwReq","DrvrSeatTiltSwReq_SW_l","DrvrSeatTiltSwReq_UI","DrvrSeatVertPosRclCmd","DrvrSeatVertSwReq","DrvrSeatVertSwReq_SW_l","DrvrSeatVertSwReq_UI","DrvrStrgDlvrdToq","DrvrStrgDlvrdToqRddy","DrvrStrgDlvrdToqV","DrvrUseIDBradcst","DrvrUseIDRoleInfo","DrvrWndDclnSpc","DspcOpenSts","DstbtrInRefTemp","DTCInfomationBMS","DTCInfomationCCU","DTCInfomationDCM_FL","DTCInfomationEOPC","DTCInfomationIMCU","DTCInfomationSDM","EAC_speed","EACVol","EASCCalibrationErr_l","EASCPosGroupMmrySts","EASCPosGroupRclReq","EASCPosGroupRclSts","EASCPosGroupSaveReq","EASCPosGroupSaveSts","EASCSysFltSts","EasyEntMdInd","EasyEntPosRclReq","EasyEntPosRclSts","EasyEntPosSaveReq","EasyEntPosSpRclReq","EasyLdMdInd","EasyPssMdInd","EBDA","EBSAcumtdBatChrg","EBSAcumtdBatDschrg","EBSBatCrnt_l","EBSBatCrnt_R_l","EBSBatVol_l","EBSBatVol_R_l","EBSSOC_l","EBSSOC_R_l","EBSSOCSts_l","EBSSOCSts_R_l","EBSSOHOfLAM","EBSSOHOfLAM_l","EBSSOHOfLAMSts_l","EBSSOHOfSlphtn","EBSSOHOfSlphtn_l","EBSSOHOfSlphtn_R_l","EBSSOHOfSlphtnSts_l","EBSTemSts_l","EBSVolSts_l","EDSPumpFaultInfo","EDUOilPmpMotCrnt","EDUOilPmpMotSpd","EDUOilPmpMotSpdV","EDUOilPmpMotSts","EDUOilPmpMotVol","EDUOilPmpMotVolV","EDUOilTem","ElecACSpd","ElecCsumpPerKm","ElecCsumpPerKmV","ElecEnrgAvgRstPerfd","EmgcBrkA","EmgcCallFlrSts","EnrgSplReq","EnrgSplReqEPTCrkAbotd","EnrgSplReqEPTCrkAbotdRsn","EnrgSplReqEPTRdy","EnrgSplReqEPTRunAbotd","EnrgSplReqEPTRunAbotdRsn","EnrgSplReqScene","EnrgSplReqV","EOPCEDUOilPmpMotCrntV","EPBAppcnSts","EPBAppcnStsRC","EPBAvlblySts","EPBFlrSts","EPBSts","EPBSwSts","EPBSysStsIndReq","EPBSysWrnngIndReq","EPMCU_NKI_PTEXTDCAN","EPMCUMotFltIO","EPMCUParkngSts","EPMCUSCUParkReqResp","EPMOprtMd","EPS_CHAvlbly","EPS_NM_BSMtoRMS_CHCANFD","EPS_NM_NOStoRMS_CHCANFD","EPS_NM_PBSMtoRMS_CHCANFD","EPS_NM_RMSSta_CHCANFD","EPS_NM_RMStoNOS_CHCANFD","EPS_NM_RSStoNOS_CHCANFD","EPS_NM_RSStoRMS_CHCANFD","EPSAIPkgAngReqResp","EPSAIPkgAngReqRespInh","EPSAIPltAngRespSts","EPSAIPltAngRespStsCRC","EPSAIPltAngRespStsInh","EPSAIPltAngRespStsInhRddy","EPSAIPltAngRespStsRC","EPSAIPltAngRespStsRddy","EPSAIPltAvlLvl","EPSAIPltAvlLvlRddy","EPSAIPltCpcty","EPSAIPltCtrlChannel","EPSAIPltCtrlChannelRddy","EPSFlrSts","EPTAccelActuPos","EPTAccelActuPosV","EPTAccelEfctvPos","EPTACLdAld","EPTBrkPdlDscrtInptSts","EPTBrkPdlDscrtInptStsV","EPTCoolngFanSts","EPTCrkAbotd","EPTFlt","EPTFltLvl","EPTHVDCDCMdReq","EPTInfoDsp2","EPTMainrelayDrvReq","EPTMotClntFlt","EPTRdy","EPTRdyBkup","EPTRgtnLvl","EPTRunCrkTrmlSts","EPTSAMMaxToqLmt","EPTSAMMdReq","EPTSAMSpdReq","EPTSAMToqReq","EPTSysPwrLmtA","EPTTMMaxToqLmt","EPTTMMaxToqLmtV","EPTTMMdReq","EPTTMSpdReq","EPTTMToqReq","EPTToqCustSetngDspCmd","EPTTrInptShaftToq","EPTTrOtptShaftToq","EPTTrOtptShaftTotToq","ErrRespHODM_l","ESS_PTAvlbly","ESSBscInfo","ESSPTCHVILSts","ESSPTCHVILStsV","EvapACMangmntInfo","EvapEXVActPosition","EvtPotclSigCCU","EvtPotclSigSCM","ExtnlBrkPrio","FasnDrvrSbltIndCmd","FasnSecRowLSbltAtc","FasnSecRowMidSbltAtc","FasnSecRowRSbltAtc","FasnThrdRowLSbltAtc","FasnThrdRowMidSbltAtc","FasnThrdRowRSbltAtc","FCRSysSts","FCTBSysFltSts","FCWDspCmd","FCWrnngSts","FCWSysFltSts","FDR_NKI_BKPCANFD","FDRSts","FICMRLSSnstvtLvlSts","FICMSWHtngReq_l","FLCrnRdrSts","FLDCMDoorOpenSts","FLDCMDoorOpenSts_L","FLDCMFLWndLclSwSts","FLDCMFRWndLclSwSts","FLDCMMstrLckCtrlReq","FLDCMOtsdMirFoldCmd","FLDCMOtsdMirMmryCtrlPosStoC","FLDCMRLWndLclSwSts","FLDCMRRWndLclSwSts","FLDoorCtrlModuOtsdMirFoldCmd","FLDoorHadlSwA","FLDoorOpenPos","FLDoorOpenPosSetngReq","FLDoorOpPosSetngDsplyCmd_l","FLDoorOtsdAirTemCrVal_l","FLDoorRdrSts_l","FLHdrtPos","FLHdrtPosRclCmd","FLHdrtSwReq","FLHdrtSwReq_SW_l","FLHdrtSwReq_UI","FLLegRetFwdPos","FLLegRetFwdPosRclCmd","fllegretfwdswreq","FLLegRetFwdSwReq_SW_l","FLLegRetFwdSwReq_UI","FLObsRng","FLOtsdHadlA","FLPCPRDoorPrblm","FLPODBCMCmd","FLPODPrblm","FLPWLStatus","FLPwrDoorOpSts","FLSeatBrkngSwReq_l","FLSeatHeatLvl","FLSeatHeatReq","FLSeatHeatSysFlt","FLSeatMsagOnOffSts","FLSeatVentLvl","FLSideObsDist","FLSideObsRng","FLSurndCamrSts","FLTireIDCfgErr","FLTireNoSig","FLTirePrs","FLTirePrsLvl","FLTirePrsPcnt","FLTirePrsV","FLTireSts","FLWndDclnSpc","FLWndOpReq","FOTAStatus","FOTAStatusValid","FOTATarget","FOTATargetValid","FRCrnRdrSts","FRDCMDoorOpenSts","FRDCMDoorOpenSts_L","FRDCMMstrLckCtrlReq","FRDCMOtsdMirMmryCtrlPosStoC","FRDoorHadlSwA","FRDoorOpenPos","FRDoorOpenPosSetngReq","FRDoorOpPosSetngDsplyCmd_l","FRDoorOtsdAirTemCrVal_l","FRDoorRdrSts_l","FrgrFltSts","FrgrWkngSts","FrgrWkngSts_l","FRHdrtPos","FRLegRetFwdPos","FRObsRng","FROtsdHadlA","FRPCPRDoorPrblm","FRPODBCMCmd","FRPODPrblm","FRSeatBrkngSwReq_l","FRSeatFoldSwtReq_l","FRSeatHeatLvl","FRSeatHeatReq","FRSeatHeatSysFlt","FRSeatMsagOnOffSts","FRSeatVentLvl","FRSideObsDist","FRSideObsRng","FRSM_NKI_BDCAN","FRSurndCamrSts","FRTireIDCfgErr","FRTireNoSig","FRTirePrs","FRTirePrsLvl","FRTirePrsPcnt","FRTirePrsV","FRTireSts","FrtLeftObstacleDist","FrtMidLObsRng","FrtMidRObsRng","FrtObsDist","FrtObstacleDist","FrtPDCAudWrnng","FrtPlacardCfgErr","FrtPnrmCamrSts","FrtPsgnAirbagSwSts","FrtPsngAirbagDsblIndF","FrtPsngAirbagEnbIndF","FrtPsngDoorOpenSts","FrtPsngOccupntSts","FrtPsngPWLInitnRmndr","FrtPsngSbltAtc","FrtPsngWndDclnSpc","FrtRdrSts","FrtRightObstacleDist","FrtSideLghtF","FrtSurndCamr1Sts","FrtSurndCamr2Sts","FrtUss1FltSts","FrtUss2FltSts","FrtUss3FltSts","FrtUss4FltSts","FrtWiperMdSts","FrtWiperOpReq","FrtWiperParkPosA","FrtWiperSwSts","FRWndDclnSpc","FRWndOpReq","HDCSysSts","HeadingRTK","HeatPumpErMngmntInfo","HeatPumpMd","HOD_FrP00_Checksum_l","HOD_FrP00_RolingCounter_l","HODDetnSts","HODDetnSts_l","HODDetSts","HODdiagSts","HODdiagSts_l","HODMHardVer_l","HODMSoftVer_l","HODSysSts","HODSysSts_l","HODTchZone1Sts","HODTchZone1Sts_l","HODTchZone1Val","HODTchZone1Val_l","HODTchZone2Sts_l","HODTchZone2Val_l","HODTchZone3Sts_l","HODTchZone3Val_l","HPCRationalFaultInfo","HtrPumpFaultInfo","HVACRationalFaultInfo","HVBatElecEnrgAvgRate","HVBatElecEnrgAvgRateV","HVBatShutOff","HVDCDCActvDisOverTime","HVDCDCClntPumpPWMReq","HVDCDCDrtng","HVDCDCHdwrFlr","HVDCDCHVSideCrnt","HVDCDCHVSideCrntV","HVDCDCHVSideVol","HVDCDCHVSideVolV","HVDCDCIntnlFlr","HVDCDCLdRatioV","HVDCDCLVSideCrnt","HVDCDCLVSideCrntV","HVDCDCLVSideStptFdbk","HVDCDCLVSideStptFdbkV","HVDCDCLVSideVol","HVDCDCLVSideVolV","HVDCDCOverCurrProtLV","HVDCDCOverHtd","HVDCDCSta","HVDCDCTem","HVDCHVILSts","HVDCHVILStsV","HVEstbCond","HVFailRsn","IAM_NKI_CONNCANFD","IAM_NOI_CONNCANFD","IAM_NWI_CONNCANFD","IAMAvlbly","IBS_NKI_CHCANFD","IBS_NKI_SFCANFD","IBS_NOI_CHCANFD","IBS_NOI_SFCANFD","IBS_NWI_CHCANFD","IBS_NWI_SFCANFD","ICC_NKI_BDCAN","ICC_NKI_BDEXTDCAN","ICC_NKI_BKPCANFD","ICC_NKI_CHCANFD","ICC_NKI_CONNCANFD","ICC_NKI_INFOCAN","ICC_NKI_INFOCANFD","ICC_NKI_PTCANFD","ICC_NKI_PTEXTDCAN","ICC_NKI_SFCANFD","ICC_NOI_BDCAN","ICC_NOI_BDEXTDCAN","ICC_NOI_BKPCANFD","ICC_NOI_CHCANFD","ICC_NOI_CONNCANFD","ICC_NOI_INFOCAN","ICC_NOI_INFOCANFD","ICC_NOI_PTCANFD","ICC_NOI_PTEXTDCAN","ICC_NOI_SFCANFD","ICC_NWI_BDCAN","ICC_NWI_BDEXTDCAN","ICC_NWI_BKPCANFD","ICC_NWI_CHCANFD","ICC_NWI_CONNCANFD","ICC_NWI_INFOCAN","ICC_NWI_INFOCANFD","ICC_NWI_PTCANFD","ICC_NWI_PTEXTDCAN","ICC_NWI_SFCANFD","ICCRmtSeatHeatFlrRsn","ICCRmtSeatHeatSts","ICCRmtStrgWhlHeatFlrRsn","ICCRmtStrgWhlHeatSts","ICM_InfoAvlbly","ICM_InfoCANFDAvlbly","ICM_NKI_INFOCAN","ICM_NKI_INFOCANFD","ICM_NOI_INFOCAN","ICM_NOI_INFOCANFD","ICM_NWI_INFOCAN","ICM_NWI_INFOCANFD","ICMErSts","ICMFLDoorOpenPosSetngDsplyCmd","ICMFLDoorOpenPosSetngReq_UI","ICMFRDoorOpenPosSetngDsplyCmd","ICMFRDoorOpenPosSetngReq_UI","ICMRLDoorOpenPosSetngDsplyCmd","ICMRLDoorOpenPosSetngReq_UI","ICMRRDoorOpenPosSetngDsplyCmd","ICMRRDoorOpenPosSetngReq_UI","ICMTurnOnRdyStsX","IDigKeyExeAvlbl","IDigKeyExeAvlblV","IEasyEntSetSts","IECU_NKI_CHCANFD","IECU_NKI_SFTYCANFD","IgnOffTime","IMATE_NKI_INFOCAN","IMATE_NWI_INFOCAN","IMATESysFltRsn","IMCU_025ms_Group05_RC","IMCU_ExtdAvlbly","IMCU_NKI_PTCANFD","IMCU_NKI_PTEXTDCAN","IMCU_NOI_PTCANFD","IMCU_NOI_PTEXTDCAN","IMCU_NWI_PTCANFD","IMCU_NWI_PTEXTDCAN","IMCU_PTAvlbly","IMCUChrgTrgtSOCDspCmd","IMCUCrntAvgElecCsump","IMCUCrntAvgElecCsumpV","IMCUElecCsumpPerKm","IMCUElecCsumpPerKmV","IMCUHVBatElecEnrgAvgRate","IMCUKeyStatus","IMCUReserCtrlDspCmd","IMCUReserSpHourDspCmd","IMCUReserSpMinuteDspCmd","IMCUReserStHourDspCmd","IMCUReserStMinuteDspCmd","IMCUVehActuElecCsump","IMCUVehActuElecCsumpV","IMCUVehElecRng","IMCUVehElecRngDspCmd","IMCUVehElecRngV","ImmoSetA","IMU_GNSS_Signal_Status","InsdAirPrtclMtrCDC","IPD_CHAvlbly","IPD_NKI_BKPCANFD","IPD_NKI_CHCANFD","IPD_NKI_SFCANFD","IPD_NWI_BKPCANFD","IPD_NWI_CHCANFD","IPD_NWI_SFCANFD","IPDLDWLKADspCmd","IPDLDWLKALVsulznReq","IPDLDWLKARVsulznReq","IPDLDWSysSts","IPDThermalWrnngLvl","IPKDErSts","ITMSACComprMngmntInfo","ITMSACStatus","ITMSAGS1Pos","ITMSAGSSelfLearnSts","ITMSBatteryWarmStatus","ITMSBattReq","ITMSBMSClntPumpFltInfo","ITMSBMSPumpActuPWM","ITMSEDSPumpActuPWM","ITMSEDUClntInltTem","ITMSHeatPumpErMngmntInfo","ITMSHeatPumpMd","ITMSHeatRecyMod","ITMSHtrPumpActuPWM","ITMSOnRutBattWarmSwDsp","ITMSSxVlvActPos","ITMSThrVlvActPWM","ITMSThrVlvSelfLearnSts","ITMSTripBookACSts","ITMSTripBookBatteryWarmSts","ITMSTripBookHour","ITMSTripBookMinute","ITMSTripBookSts","ITMSTripBookWeek","ITMSTripBookWeekly","ITMSWPTCActuPwr","ITMSWPTCOutTem","keep_network_AMR","keep_network_BPEPS","keep_network_BPEPS_UB","keep_network_MSM","keep_network_MSM_Psng","keep_network_PLCM","KeepPTRdyReq","Key1Loctn","Key2Loctn","KeyDetIndx","KeyFobFLPwrDrCtrlSwA","KeyFobFRPwrDrCtrlSwA","KeyFobRLPwrDrCtrlSwA","KeyFobRRPwrDrCtrlSwA","LaneChngSts","Latitude","LBrkLghtF","LBSDAndLCAWrnng","lckngsysstsind","LckngSysStsInd_FR_l","LckngSysStsInd_l","LDipdBeamLghtF","LDircnIndLghtF","LDircnIO","LDOWWrnng","LDrvnWhlRotlDircn","LdspcOpenSts","LDWLKADspCmd","LDWLKALVsulznReq","LDWLKARVsulznReq","LDWSysFltSts","LDWSysSts","LevelMode_Request_IEnt","LFSDA_NKI_BKPCANFD","LFSDASts","LghtSwPosSts","LHCMSHoztPos","LHCMSHoztPosRclReq","LHCMSMmryCtrlPosRclSts","LHCMSVertPos","LHCMSVertPosRclReq","LHRDA_NKI_BKPCANFD","LiftMdInd","LLidarSts","LNonDrvnWhlRotlDircn","Longitude","LOtsdMirHoztPos","LOtsdMirHoztPosRclReq","LOtsdMirMmryCtrlPosRclSts","LOtsdMirVertPos","LOtsdMirVertPosRclReq","LowWiperWshrFludLvlSwA","LPnrmCamrSts","LScrnLftrSwReq_l","LSideLghtF","LUss1FltSts","LUss2FltSts","lvbm_group03_rc","LVBMAlrmSts","LVBMCrnt","LVBMFltLvl","LVBMHdwrFltSts","LVBMMOSSts","LVBMSOC","LVBMSOCV","LVBMSOH","LVBMSOHV","LVBMTotVol","LVentFootTem","LvlCtrlAdjActn","ILvlCtrlAdjSts","LvlCtrlHghtSnsrCalCmpd","LvlCtrlReqdTrgtLvl","LvlCtrlRestrctnRsn","LvlCtrlVehMd","MainBeamLghtOn","MainBeamSwPosSts","MdFlapActuAirmixAngL","MdFlapActuAirmixAngR","MdOfPwrMd","MixAirLftMotFaultInfo","MixAirRtMotFaultInfo","MSM_NKI_BDCAN","MSMDrvrSeatHoztMotActn","MSMDrvrSeatHoztPos","MSMDrvrSeatRclnMotActn","MSMDrvrSeatRclnPos","MSMDrvrSeatTiltMotActn","MSMDrvrSeatTiltPos","MSMDrvrSeatVertMotActn","MSMDrvrSeatVertPos","MSMDrvrSeatZeroPosLrnngSts","MSMPsngSeatHoztPos","MSMPsngSeatRclnPos","MSMPsngSeatVertPos","MSMPsngSeatZeroPosLrnngSts","MSRA","NarwSpcAstBtnResp","NarwSpcAstBtnSts","NarwSpcAstSwSts","network_mode","NFCIDNum1","NFCIDNum2","NFCIDNum3","NFCIDNum4","NFCIDNum5","NFCInCardIDE","NFCOutCardIDE","NFCSMPlmntSts","NFCSMSta","NOAQukSwSts","NOASwSts","NRCDToqReqSts","NRCDToqReqVal","Obj0LatDistance","Obj0LatSpd","Obj0LongDistance","Obj0LongSpd","Obj0Style","OCondOutRefTemp","OfbdChrgrNgtvSktTem","OfbdChrgrNgtvSktTemV","OfbdChrgrPstvSktTem","OHXEXVActPosition","OilPmpMotFltLvl","OnBdChrgrAltrCrntInptCrnt","OnbdChrgrAltrCrntInptHVCrntLmt","OnBdChrgrAltrCrntInptVol","OnBdChrgrCtrlPilotPWMDuty","OnBdChrgrCtrlPilotPWMSts","OnBdChrgrFltCt","OnBdChrgrFltSts","OnBdChrgrHVILStsVal","OnBdChrgrInsdTem3","OnBdChrgrL1AltrCrntInptCrnt","OnBdChrgrL1AltrCrntInptVol","OnBdChrgrL2AltrCrntInptCrnt","OnBdChrgrL2AltrCrntInptVol","OnBdChrgrL3AltrCrntInptCrnt","OnBdChrgrL3AltrCrntInptVol","OnBdChrgrLastWkup","OnbdChrgrLLCMaxTem","OnbdChrgrOpngMd","OnbdChrgrOtptCrntVal","OnBdChrgrOtptVolV","OnbdChrgrOtptVolVal","OnbdChrgrPFCMaxTem","OnbdChrgrSktNgtvSnsrTem","OnBdChrgrSktPstvSnsrTem","OnBdChrgrSktPstvSnsrTemV","OnBdChrgrSts","OnBdChrgrWkup","OtrIntlgntMdTrigHvacReq","OtrIntlgntMdTrigReq","OtsAirTmp","OtsdAirTemCrVal","OtsdMirPosGroupMmrySts","OtsdMirPosGroupRclReq","OtsdMirPosGroupRclSts","OtsdMirPosGroupSaveReq","OtsdMirPosGroupSaveSts","OtsdMirPosRclReq","PDCSysSts","PDUHVILSts","PDUHVILStsV","PEBOfbdChrgCapVol","PedtrnProtnSysIndrCmd","PEPSAntFlt","PGM_NKI_INFOCAN","PLCM_NOI_BDCAN","PLCM_NWI_BDCAN","PLCMLdspcOpenSts","PMAFLObsRng","PMAFLSideObsDist","PMAFLSideObsDistV","PMAFLSideObsRng","PMAFRObsRng","PMAFRSideObsDist","PMAFRSideObsDistV","PMAFRSideObsRng","PMAFrtMidLObsRng","PMAFrtMidRObsRng","PMAFrtObsDist","PMAFrtObsDistV","PMAPDCSysSts","PMARLObsRng","PMARLSideObsDist","PMARLSideObsDistV","PMARLSideObsRng","PMARrMidLObsRng","PMARrMidRObsRng","PMARrObsDist","PMARrObsDistV","PMARRObsRng","PMARRSideObsDist","PMARRSideObsDistV","PMARRSideObsRng","PMDCSta","PollngSts","PositionAttitudeStatus","PowerOnReset","PsngAutoAirVentSwngSts","PsngAvdAirVentSts","PsngEasyEntPosRclSts","PsngFcsOnAirVentSts","PsngHdrtPosLkagRclCmd","PsngLegRetFwdPosLkagRclCmd","PsngPosSaveReq","PsngSeatHoztPosLkagRclCmd","PsngSeatHoztPosRclCmd","PsngSeatHoztSwReq","PsngSeatHoztSwReq_SW_l","PsngSeatHoztSwReq_UI","PsngSeatLkagRclnPosLkagRclCmd","PsngSeatPosRclCmd","PsngSeatPosRclReq","PsngSeatPosRclSts","PsngSeatRclnPosRclCmd","PsngSeatRclnSwReq","PsngSeatRclnSwReq_SW_l","PsngSeatRclnSwReq_UI","PsngSeatVertPosLkagRclCmd","PsngSeatVertPosRclCmd","PsngSeatVertPosRclCmd_UI","PsngSeatVertSwReq","PsngSeatVertSwReq_SW_l","PsngSeatVertSwReq_UI","PtACCToqReqResp","PTCActuPwr","PTCMngmntInfo","PwrLftgtPos","PwrLftgtPosSetngReq","PwrLftgtPosV","PwrLftgtSts","PwrLftgtSwIndrCmd_l","PwrLftgtSwReq_l","PyrofuseInfo1_DA04","PyrofuseInfo2_DA04","PyrofuseInfo3_DA04","PyrofuseInfoIndex_DA04","RBM_NKI_CHCANFD","RBM_NKI_SFCANFD","RBrkLghtF","RBSDAndLCAWrnng","RC_Bluetooth","RCTBSysFltSts","RCWSelSts","RCWWrnng","RDASysSta","RDipdBeamLghtF","RDircnIndLghtF","RDircnIO","RDOWWrnng","RDrvnWhlRotlDircn","RdyFailRsn","RearLeftObstacleDist","RearObstacleDist","RearRightObstacleDist","RevsLghtF","RFICRebootCnt","RFICRebootReason","RFReceiveMode","RFSDA_NKI_BKPCANFD","RFSDASts","RFWelcomeMode","RgnLvReqFICM","RgstnPltLghtF","RgtvBrkFnSts","RHCMSHoztPos","RHCMSHoztPosRclReq","RHCMSMmryCtrlPosRclSts","RHCMSVertPos","RHCMSVertPosRclReq","RHRDA_NKI_BKPCANFD","RHRDAAvlbly","RHRDASts","RLChildProtnA","RLChildProtnSts","RLChildProtnThrmlSts","RLCrnRdrSts","RLDCMDoorOpenSts","RLDCMMstrLckCtrlReq","RLDoorHadlSwA","RLDoorOpenPos","RLDoorOpenPosSetngReq","RLDoorOpenSts","RLDoorOpPosSetngDsplyCmd_l","RLDoorRdrSts_l","RLidarSts","RLObsRng","RLOtsdHadlA","RLPCPRDoorPrblm","RLPODBCMCmd","RLPODPrblm","RLPWLInitnRmndr","RLSideObsDist","RLSideObsRng","RLSurndCamrlSts","RLTireIDCfgErr","RLTirePrs","RLTirePrsLvl","RLTirePrsPcnt","RLTirePrsV","RLTireSts","RLWndDclnSpc","RLWndLclSwSts","RLWndOpReq","RmnDrvngDist","RmtACAbotRsn","RmtACReq","RmtACRmningTime","RmtACSts","RmtACTrgtTemReq","RmtScurReq","RmtScurResp","RNonDrvnWhlRotlDircn","ROtsdMirHoztPos","ROtsdMirHoztPosRclReq","ROtsdMirMmryCtrlPosRclSts","ROtsdMirVertPos","ROtsdMirVertPosRclReq","RPnrmCamrCalSts","RPnrmCamrSts","RrACOnOffDspCmd","RRChildProtnA","RRChildProtnSts","RRChildProtnThrmlSts","RRCrnRdrSts","RRDCMDoorOpenSts","RRDCMMstrLckCtrlReq","RRDoorHadlSwA","RRDoorOpenPos","RRDoorOpenPosSetngReq","RRDoorOpenSts","RRDoorOpPosSetngDsplyCmd_l","RRDoorRdrSts_l","RrFogLghtF","RrMidLObsRng","RrMidRObsRng","RrObsDist","RRObsRng","RROtsdHadlA","RRPCPRDoorPrblm","RrPDCAudWrnng","RrPnrmCamrCalSts","RrPnrmCamrSts","RRPODBCMCmd","RRPODPrblm","RRPWLInitnRmndr","RrRSurndCamrSts","RrSideLghtF","RRSideObsDist","RRSideObsRng","RrSurndCamrSts","RRTireIDCfgErr","RRTireNoSig","RRTirePrs","RRTirePrsLvl","RRTirePrsPcnt","RRTirePrsV","RRTireSts","RrtPlacardCfgErr","RrUss1FltSts","RrUss2FltSts","RrUss3FltSts","RrUss4FltSts","RRWndDclnSpc","RRWndLclSwSts","RRWndOpReq","RScrnLftrSwReq_l","RSeatPosLkagRclCmd","RSeatPosLkagRclSts","RSideLghtF","RUss1FltSts","RUss2FltSts","RVentFootTem","RVSEPTCrkAbotd","RVSEPTCrkAbotdRsn","RVSEPTRdy","RVSEPTRunAbotd","RVSEPTRunAbotdRsn","RVSReq","RVSReqA","RVSSts","RWSGW_NKI_CHCANFD","RWSWarning","S11LFLSeatHeatLvl","S11LFLSeatHeatReq","S11LFLSeatHeatSysFlt","S11LFRSeatHeatLvl","S11LFRSeatHeatReq","S11LFRSeatHeatSysFlt","S11LSecRowLSeatHeatLvl","S11LSecRowLSeatHeatReq","S11LSecRowLSeatHeatSysFlt","S11LSecRowRSeatHeatLvl","S11LSecRowRSeatHeatReq","S11LSecRowRSeatHeatSysFlt","s11pwrlftgtswreq","SAC_NKI_PTCANFD","SAC_NKI_PTEXTDCAN","SAMActuToq","SAMFltLamp","SAMFltLvlSts","SAMHVILSts","SAMHVILStsBkup","SAMInvtrCrnt","SAMInvtrVol","SAMMaxAvlblToq","SAMSpd","SAMSta","SAMSttrTem","SAS_NKI_CHCANFD","SatNoInPositionRTK","SatNum","SCM_NKI_CHCANFD","scmscsmdreq","SCSAPAAcclA","SCSVehHoldA","SCU_NKI_PTEXTDCAN","SCU_NWI_PTEXTDCAN","SCUExtdShifterFlr","SCUParkLckReq","SCUParkLckReqEPMCU","ScurReqAC","ScurReqADS","ScurReqBCM","ScurRespAC","ScurRespADS","ScurRespBCM","ScurtAlrmSts","ScurtAlrmTrigd","ScurtKeyBatLow","ScurtKeyInvd","SCUShifterLvrPos","SCUShifterLvrRawPos","SDM_NKI_SFCANFD","SDM_NWI_SFCANFD","SDMAvlbly","SDMRC","SeatOccptnNum","SecRowLOccupntSts","SecRowLSeatHeatLvl","SecRowLSeatHeatReq","SecRowLSeatHeatSysFlt","SecRowMOccupntSts","SecRowROccupntSts","SecRowRSeatHeatLvl","SecRowRSeatHeatReq","SecRowRSeatHeatSysFlt","SecsOfMinute","SecyAxleSts","SLIFSts","SolarStrngRSide","StrgWhlAng","StrgWhlAngGrd","StrgWhlAngSnsrCalSts","StrgWhlAngSnsrFlt","StrgWhlAngV","SusDmpngCtrlCustSetngDspCmd","SusDmpngCtrlFlrSts","SusDmpngCtrlLampReq","SusDmpngCtrlMdSts","SusFlrSts","SusHghtFL","SusHghtFLV","SusHghtFR","SusHghtFRV","SusHghtRL","SusHghtRLV","SusHghtRR","SusHghtRRV","SusLvlCtrlCustSetngDspCmd","SusLvlCtrlFlrSts","SusLvlCtrlHghtSts","SusLvlCtrlLampReq","SvGroup","SWHtngReqDspCmd","SWHtngReqDspCmd_l","SWHtngWrng_l","SWVFaultInfo","SysOpnlMd","SysPwrMd","SysPwrMd_l","SysPwrMd_l_DCM_FL","SysPwrMd_l_DCMFR_LIN","SysPwrMd_l_ICC_LIN1","SysPwrMdV","SysVol","TakeKeyOutRmndr","TBOXSysTim","TCSA","TCSEnbd","TCSOpngMd","TCSOpngSts","TeleMotPosCmd_l","TeleMotPosSts","TeleMotPosSts_l","TiltMotPosCmd_l","TiltMotPosSts","TiltMotPosSts_l","TMActuToq","TMFltLamp","TMFltLvlSts","TMHVILStsBkup","TMInvtrCrnt","TMInvtrOvTempAlrm","TMInvtrTem","TMInvtrVol","TMInvtrVolV","TMMaxAvlblToq","TMMaxAvlblToqV","TMRtrTem","TMSpd","TMSta","TMStrOvTempAlrm","TMSttrTem","TMToqReq","TPMSAutoLoctnCm","TPMSF","TPMSIdficnLrnCm","TPMSTirePrsLowIO","TrShftLvrPos","TrShftLvrPosPPC","TrShftLvrPosV","TWVFaultInfo","VCMSRationalFaultInfo","VCUSecyWrnngInfo","VehCrshTyp","VehDrvngMd","VehDrvngMdV","VehDynYawRate","VehDynYawRateV","VehElecRng","VehElecRngDspCmd","VehElecRngV","VehEnrgRdyLvl","VehEnrgRdyLvlV","VehHzrdMdSts","VehLckngSta","VehLdShedLvl","VehOdo","VehOdoV","VehSdslSts","VehSideLghtSts","VehSideLghtSts_l","VehSideLghtSts_l_DCMFL_LIN","VehSpdAvg","VehSpdAvgAlvRC","VehSpdAvgDrvn","VehSpdAvgDrvnV","VehSpdAvgNonDrvn","VehSpdAvgNonDrvnV","VehSpdAvgV","VehSpdDpdntLvlCtrlSts","VSELatAcc","VSELatAccV","VSELongtAcc","VSELongtAccV","VSEMd","VSESts","VSESysA","wake_network_AMR","wake_network_BPEPS","wake_network_BPEPS_UB","wake_network_MSM","wake_network_MSM_Psng","wake_network_PLCM","WCDSInClntTemp","WCondOutRefTem","WhlBrkPrsSts","WhlGndVelLDrvn","WhlGndVelLDrvnV","WhlGndVelLNonDrvn","WhlGndVelLNonDrvnV","WhlGndVelRDrvn","WhlGndVelRDrvnV","WhlGndVelRNonDrvn","WhlGndVelRNonDrvnV","WLC_NKI_PTEXTDCAN","WLC_NOI_PTEXTDCAN","WLC_NWI_PTEXTDCAN","WLCAvlbly","WLCHVILSts","WLCHVILStsV","WlcmLghtReq","WlcmMdTrigHvacReq","WndsrnHmdty","WPTCMngmntInfo","WrlsChrgrActuOtptCrnt","WrlsChrgrAuxFnDetResult","WrlsChrgrAuxFnDetResultV","WrlsChrgrIdHiRng","WrlsChrgrIdV","WrlsChrgrSts","WrlsChrgrWifiSts","WrlsChrgrWkupA","ZeroGrvySwtEntReq_l","ZeroGrvySwtExitReq_l","ACSyncDspCmd","BMSChrgBufV","BMSChrgPeakPwrV","BMSDschrgCrntLmtV","BMSDschrgCtnsPwrLmtV","BMSDschrgPeakPwr","BMSDschrgPeakPwrV","BMSOnbdChrgngMd","BMSStaOfHealthResi","PTCOtptTem","RWSActuDrvngMd","VehRefSpd","RWSActuRdWhlAng","CrabWalkActvResp","LVBMEstdCapct","IBSAPASysFltSts","HeatPmpOpMd","EACSpd","HeatPmpOpSts","IWtrPTCSts","IEACSts","AmbtTemFaltSts","ACEvapoTemFaltSts_Rr","ACFcVentTemFaltSts_FrtLft","ACFcVentTemFaltSts_FrtRt","ACFtVentTemFaltSts_FrtLft","ACFtVentTemFaltSts_FrtRt","ACFtVentTemFaltSts_RrLft","ACFcVentTemFaltSts_RrLft","ChlrInltClntTemFaltSts","ChlrOtltRefgTemFaltSts","EDUInltClntTemFaltSts","ITMSESSClntTemFaltSts","IntkComprRefgTemFaltSts","OtltComprRefgTemFaltSts","WtrPTCOutlClntTemFaltSts","WtrPTCMidClntTemFaltSts","ACFrtInCarTemV","ITMSAGS1FltInfo","ITMSAGS2FltInfo","ITMSFanFltInfo","ACRrBlwrFaltSts","ITMSEDSPumpFltInfo","ITMSHtrPumpFltInfo","ThrVlvFaultInfo","IMuVlvFaultInfo","ThrVlvFaultInfo_REV","IMuVlvFaultInfo_REV","ChlrEXVFaltSts","EvaprEXVFaltSts","ITMSTraceMdEnhancecoolMngmtInfo","RefLeakFltInfo","ACMdMotFaltSts_FrtLftMd","ACMdMotFaltSts_FrtRr","ACMdMotFaltSts_FrtRtMd","ACMixAirMotFaltSts_FrtLft","ACMixAirMotFaltSts_FrtRt","ACMixAirMotFaltSts_RrLft","ACRccMotFaltSts","ACFreshMotFaltSts","ACHiSideFludPrsFaltSts","EACCrntSnsrFlt","EACVolSnsrFlt","ITMSAThrmlShtdwnFlt","ITMSAThrmlWarnFlt","RrHVACSVFaltSts","EvaprEXVActPosn","WtrPTCInltClntTem","EDUInltClntTem","ChlrInltClntTem","WtrPTCSts","EACSts","VSELongtAcc_SDM","ITMSESSClntTem","AccConsmpAftChrg","ACOnOffReq","AltngChrgCrntDspCmd","AMPASndWaveSelSts","BatSOFVol1Sts","BatTemSts","BMSBatteryLifeInfo1_DA00","BMSBatteryLifeInfo2_DA00","BMSCellAlrdyBalcTime_DA02","BMSCellBalcIdx_DA02","BMSCellMinTemIndx","BMSCellRmngBalcTime_DA02","BMSChrgCtnsPwrLmt","BMSDircPstvRelaySts","BMSDircPstvRelayStsV","BMSDsChrgCtrlDspCmd","BMSFltIndInfo_DA02","BMSOfbdChrgRelayStsV","BMSPTIsolationLevel","BMSRptBatCodeAsc1","BMSRptBatCodeAsc2","BMSRptBatCodeAsc3","BMSRptBatCodeAsc4","BMSRptBatCodeAsc5","BMSRptBatCodeAsc6","BMSRptBatCodeAsc7","BMSRptBatCodeIndx","BPEPSPwrSts","CCUPwrSts","CentrConsoleLampSts","ChrgngRmnngTimeV","ChrgTrgtSOCDspCmd","ChrgTrgtSOCVal","CMSPwrSts","CurSenInfo_DA02","DKMMPwrSts","DLPCMPwrSts","DLPLghtThemeMdBatSts","DschrgTrgtSOCVal","DSSADBkupPwrSts","DSSADPwrSts","DTCInfomationECM","DTCInfomationESS","DTCInfomationLVBM","DTCInfomationRZCU","DTCInfomationSAC","DTCInfomationTC","EASCCalibSts_l","EASCLINRespErr_l","EASCPermanentErr_l","EASCTemporaryErr_l","EBSAcumtdBatChrg_l","EBSAcumtdBatChrg_R_l","EBSAcumtdBatDschrg_l","EBSAcumtdBatDschrg_R_l","EBSBatSOFVol1_l","EBSBatSOFVol1Sts_R_l","EBSBatSOFVol2_l","EBSBatTem_l","EBSBatTem_R_l","EBSCrntSts_l","EBSCrntSts_R_l","EBSErInCalData_R_l","EBSErInECUIdficn_R_l","EBSQucntCrnt_l","EBSQucntCrnt_R_l","EBSRespEr_l","EBSRespEr_R_l","EBSSOHOfCrsn_l","EBSSOHOfCrsnSts_R_l","EBSSOHOfLAM_R_l","EBSSOHOfLAMSts_R_l","EBSSOHOfSlphtnSts_R_l","EnRunABkup","EnSpdBkup","EPBSwStsV","EPTInfoDsp","EPTSecyAxleTrInptShaftMaxAvlblToq","EPTSecyAxleTrInptShaftMaxAvlblToqV","EPTTrInptShaftToqV","ESCLPwrSts","ESSPwrSts","ETCMPwrSts","FDR4DPwrSts","FDRPwrSts","FICMV2XEnEnbReq","FLiDARPwrSts","FLSeatMsagMdSts","FrgrLblDspCmd_l","FRSeatMsagMdSts_l","FrtSDAPwrSts","IAMPwrSts","IPDBkupPwrSts","IPDPwrSts","IPKSetVehEleclRngAlg","IPSPwrSts","ITMSEnbWPTC","LHPODRPwrSts","LHSWPwrSts","LScrnLftrStsInd_l","LVBM_NKI_PTEXTDCAN","LVBM_NOI_PTEXTDCAN","LVBM_NWI_PTEXTDCAN","LVBMAcumtdBatChrg","LVBMAcumtdBatChrgV","LVBMAcumtdBatDischrg","LVBMAcumtdBatDischrgV","LVBMCell1Vol","LVBMCell1VolV","LVBMCell2Vol","LVBMCell2VolV","LVBMCell3Vol","LVBMCell3VolV","LVBMCell4Vol","LVBMCell4VolV","LVBMChrgCrntLmt","LVBMChrgCrntLmtV","LVBMChrgVolDynCalReq","LVBMChrgVolReq","LVBMCrntSts","LVBMDschrgCrntLmt","LVBMDschrgCrntLmtV","LVBMIntnlRstcPrstTem","LVBMMaxCellTem","LVBMMaxCellTemV","LVBMMinCellTem","LVBMMinCellTemV","LVBMTotVolSts","MapLampFLSts_l","MinuteOfHour","NFCAMPwrSts","NFCSMPwrSts","PMAPwrSts","PTCActuCrnt","PTCActuVol","PTCFltLvl","PTCHVLckSts","PTCInptTem","PTCOverVol","PTCSts","PTCUnderVol","ReserChrgSts","ReserOnbdChrgCtrlReq","RHPODRPwrSts","RIPDPwrSts","RLSSPwrSts","RmtChrgTrgtSOCReq","RmtReserCtrlReq","RmtReserSpHour","RmtReserSpMinute","RmtReserStHour","RmtReserStMinute","RrMPWCPwrSts","RrSDAPwrSts","RrSwPwrPwrSts","SASPwrSts","SDMBkupPwrSts","SDMPwrSts","SysBPM","SysBPMEnbd","SysVolMd","TCPwrSts","ThrdRowUSBPwrSts","TMActuToqV","TMInvtrCrntV","TotalRegenEnrg","TotlConsmpAftChrg","TrOtptRotlStsV","VDCCtrlStat","VehActuElecCsump","VehActuElecCsumpV","VehActuElecRng","VehActuElecRngV","ZXDBkupPwrSts","ZXDPwrSts","dt","gwt","dpt","SecRowRSeatMsagPrtSts_l","kafka_receive_time","sink_paimon_time"
"LSJWT4091SS042108","1750465049275","1750465094641","1750465095164","LSJWT4091SS042108_SliceCAN_202506210817290275_E_01301000000100000000000000000000000000000000000000000000000075C6-7","10","1750465056000","7000","0","0","0","26.5","0","0","","","0","","","","","","0","","","0","","","0","0","0","","","","0","27.5","0","0","","704","28","","","7","","","0","","","","","","","","","","","","","","","","0","0","","0","","0","0","","","","","0","0","","0","30","","","","0","0","0","","1","0","0","-34.725","","","","","","","","","0","1","","0","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","0","0","","0","0","","","","0","","0","0","","2","","","","","","74.8","","","","","","","","","","","","","","","","","","","","","","","","","","","","","0","0","0","","","","","","","","","","0","","","1","0","","0","","0","","","","","","","3","","","27","","0","3.332","","0","27","3.329","","0","","","","0","1","100","-350","0","0","0","511.5","1","230.5","230.5","0","1","0","0","","0","","","","0","","","87.5","","","0","","2","","","100","0","632","256","0","417","417","0","0","","","","","0","0","0","0","0","59.75","0","","","1","1","1","","0","","2","","","0","","","","1","","","","","","0","8191.5","","1","","1","0","","0","","0","0","0","100","","204.7","1310.7","0","0","","","","1.6","2","0","0","","79","79","79","78.6","","0","","0","","1000","0","659.25","659.25","","0","0","0","","659.5","8191.5","","1","","1","0","0","0","","","16383","1","0","","","1023.75","","","2","","2","","","","","","","","","","","","","","0","0","","","","0","","","","","","0","0","","0","1","0","0","0.784314","0","0","","0","","0","0","","","","0","0","0","","","","","","","","","","","","","","","","0","","","0","0","","","","0","4","4","","","","","","1023","","","","","","","","","0","","","82.3","","","","","13.53125","","","","","","","","","","","","","","","","","","","","","","","0","","","0","0","","","0","","","","","","","0","","","","","0","","","","","","","","0","","0.34375","0","","","0","","","","","","","","","","","","","","","","","","","","","","","","","","","","","-1.8","","","","","","","","","","","","","","","661","","","","","","","","","","","","","","","","","","13.0390625","","13.4443092","","93","","2","","","50","0","100","100","","1","0","0","","","","","","","","-50","","82.3","1","","0","3","0","0","0","0","0","0","0","0","","2","","1","0","0","0","0","0","","0","0","","2","","","","","","","","","","","","","0","","","","","","","","","0","0","0","0","13","0","0","","0","","0","2","0","","","1","1","1","","","","","","0","420","","8","","-24","2","","","36.5","0","1","","","","","","","","","","","","","","","","","","","","0","","","","0","0","","","","","","","","","","","","","","","","","","","","","","","","","","","0","","","","","","","","","","","","","0","0","0","","","272","","","0","0","","","0","1","0","1","0","","","","","","","","","","","","","","","","0","","","","","","","","","","","","0","0","","0","","","284","","","0","0","0","0","0","254","0","","","0","","","","0","0","","","","0","0","","0","0","0","0","0","0","","0","","","","","0","","","","130","2","1","1","","","0","0","2","0","0","","0","","110","0","110","0","110","0","","20","","","0","0","","0","","1","0","659","0","","0","46","0","13.5","0","13.5","0","0","0","2","27","1","0","2","","","1","0","1","","","0","0","0","0","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","1800","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","0","","","0","0","0","42","28","","","","","","","","","","","","","","","","","","","","","","","","0","","","","","","","","","","","","","","","","","","","2","0","","","","0","0","","","","","","","","","","","","","2","","","","","","","","0","","","0","0","","","","","","","","","","","","","","","","","","","","","","","0","","","","","","","","","","","","","","","","","1","","0","0","","","","","","","","","0","0","2","0","0","0","","","","","","","26","0","26","","","0","0","0","0","1","","","1","28","0","0","0","0","0","0","0","30","0","0","0","659.46","27","","26","0","1","0","0","","26.5","26.5","","","","","","","","1","0","339","0","","","","","","","","","","","","","","","","","","0","","","","","","","","","","","","","0","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","4","","","","","","0","","","","","","","","","","","","0","","0","5","","","","","2","0","0","0","0","","","","","","","","1","","0","","","","","","","1","","0","","","0","","","","","","0","","","","0","","","","","","0","0","0","","276","","","0","0","","","","","0","","0","0","","","","2","","","","","","","0","","0","","","0","","","","","","0","","","","0","0","153","0","","","","","0","","","","0","","510","0","0","","","280","","","0","0","","0","0","0","0","","","","","","","","0","0","","","","0","","","0","","0","","0","","","","","","","","","","","","","","","","0","","0","0","","","","","0","0","","","","","","","0","0","","","","","0","","","","","","","","","","","","","","0","1","","","2","","","","2","2","","","","","0","0","0","-397.375","-36","2","0","0","","","","","","","","","","","","","","","","","","","","0","0","","0","2","","2","","","0","13.1","","","0","","1","0","","","","","","","-37","0","0","1","0","","26","662","","420","","","-109","8","","26","","","0","1","0","2","","0","","0","0","0","3","","","","507","0","0","0","0","0","","0","5","0","0","","0","0","1.359375","","1.359375","0","1.609375","0","0","","","","","","1","0","0","","","","","","","","","","1.65625","0","1.84375","0","1.0625","0","1.375","0","","","","","","","","","0","","","","","","","","","","","","0","0","0","","0","417","0","0","","","1","","5.913","2","","0","0","0","0","","","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","1","0","0","0","0","0","0","0","0","300","28","28","28","0","0","-0.12","27","","0","0","","","","","","38","13","","0","147","0","0","","14158260","0","","70","53","53","48","65","48","55","2","1","1","","1","100","100","0","16451","1","0","","50","","","","","","","","","","","","","130.5","","102.75","","7.8125","","8.625","","","0","","","","","","0","","9.1875","","","","","","","0","0","","","","0","1","1","","1","0","1","","","","1","1","","1","0","0","0","0","1","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","0","","1","1","1","","","","","","","","","2","2","0","","1","","","","","","","0","1","0","0","","1","","","0","1","0","0","","","","","","12","0","498","0","","1","20250621","1750465094621","1750465095181","","1750465095275","1750465095337"
